# PowerShell script to compile the project with Java 21
Write-Host "Setting JAVA_HOME to Java 21..." -ForegroundColor Green
$env:JAVA_HOME = "D:\WorkTools\jdk\jdk21"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host "Current Java version:" -ForegroundColor Yellow
java -version

Write-Host ""
Write-Host "Running Maven compile..." -ForegroundColor Green
mvn clean compile

Write-Host ""
Write-Host "Compile completed!" -ForegroundColor Green
