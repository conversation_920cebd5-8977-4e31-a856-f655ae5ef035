# PowerShell script to build the project with Java 21
Write-Host "Setting JAVA_HOME to Java 21..." -ForegroundColor Green
$env:JAVA_HOME = "D:\WorkTools\jdk\jdk21"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host "Current Java version:" -ForegroundColor Yellow
java -version

Write-Host ""
Write-Host "Running Maven build..." -ForegroundColor Green
mvn clean package -DskipTests

Write-Host ""
Write-Host "Build completed!" -ForegroundColor Green
Read-Host "Press Enter to continue..."
